package com.otaliastudios.cameraview.filters;

import android.opengl.GLES20;

import androidx.annotation.NonNull;

import com.otaliastudios.cameraview.filter.BaseFilter;
import com.otaliastudios.opengl.core.Egloo;

public class BulgeDistortionWithBlackWhiteFilter extends BaseFilter {
    private static final String FRAGMENT_SHADER =
            "#extension GL_OES_EGL_image_external : require\n" +
                    "precision mediump float;\n" +

                    "varying highp vec2 vTextureCoord;\n" +
                    "uniform lowp samplerExternalOES sTexture;\n" +

                    "uniform highp vec2 center;\n" +
                    "uniform highp float radius;\n" +
                    "uniform highp float scale;\n" +

                    "void main() {\n" +
                    "    highp vec2 textureCoordinateToUse = vTextureCoord;\n" +
                    "    highp float dist = distance(center, vTextureCoord);\n" +
                    "    textureCoordinateToUse -= center;\n" +
                    "    if (dist < radius) {\n" +
                    "        highp float percent = 1.0 - ((radius - dist) / radius) * scale;\n" +
                    "        percent = percent * percent;\n" +
                    "        textureCoordinateToUse *= percent;\n" +
                    "    }\n" +
                    "    textureCoordinateToUse += center;\n" +

                    "    vec4 color = texture2D(sTexture, textureCoordinateToUse);\n" +
                    "    float bw = (color.r + color.g + color.b) / 3.0;\n" +
                    "    gl_FragColor = vec4(bw, bw, bw, color.a);\n" +
                    "}\n";

    private float centerX = 0.5f;
    private float centerY = 0.5f;
    private float radius = 0.35f;
    private float scale = 0.45f;

    private int locationCenter = -1;
    private int locationRadius = -1;
    private int locationScale = -1;

    public BulgeDistortionWithBlackWhiteFilter() { }

    public BulgeDistortionWithBlackWhiteFilter(float centerX, float centerY, float radius, float scale) {
        this.centerX = centerX;
        this.centerY = centerY;
        this.radius = radius;
        this.scale = scale;
    }

    @NonNull
    @Override
    public String getFragmentShader() {
        return FRAGMENT_SHADER;
    }

    @Override
    public void onCreate(int programHandle) {
        super.onCreate(programHandle);
        locationCenter = GLES20.glGetUniformLocation(programHandle, "center");
        locationRadius = GLES20.glGetUniformLocation(programHandle, "radius");
        locationScale = GLES20.glGetUniformLocation(programHandle, "scale");

        Egloo.checkGlProgramLocation(locationCenter, "center");
        Egloo.checkGlProgramLocation(locationRadius, "radius");
        Egloo.checkGlProgramLocation(locationScale, "scale");
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        locationCenter = -1;
        locationRadius = -1;
        locationScale = -1;
    }

    @Override
    protected void onPreDraw(long timestampUs, @NonNull float[] transformMatrix) {
        super.onPreDraw(timestampUs, transformMatrix);
        GLES20.glUniform2f(locationCenter, centerX, centerY);
        GLES20.glUniform1f(locationRadius, radius);
        GLES20.glUniform1f(locationScale, scale);
        Egloo.checkGlError("glUniform1f");
    }
}