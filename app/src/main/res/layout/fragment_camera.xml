<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cameraLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <!-- Ảnh nền -->
        <FrameLayout
            android:id="@+id/frameLayout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@null"
            app:layout_constraintBottom_toTopOf="@+id/bottomPanel"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/topIcons">

            <com.otaliastudios.cameraview.CameraView
                android:id="@+id/flCameraView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                app:cameraDrawHardwareOverlays="true"
                tools:background="@color/black" />

            <FrameLayout
                android:id="@+id/locationContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </FrameLayout>

        <!-- Hàng icon phía trên -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/topIcons"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/black"
            android:paddingVertical="@dimen/_6dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_chainStyle="spread"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageButton
                android:id="@+id/btnBack"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:padding="@dimen/_10dp"
                android:src="@drawable/ic_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnRatio"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/btnRatio"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:padding="@dimen/_10dp"
                android:src="@drawable/ic_ratio_1_1_camera_screen"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnGrid"
                app:layout_constraintStart_toEndOf="@id/btnBack"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/btnGrid"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:padding="@dimen/_10dp"
                android:src="@drawable/ic_grid_camera_screen"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnFlash"
                app:layout_constraintStart_toEndOf="@id/btnRatio"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/btnFlash"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:src="@drawable/ic_flash_camera_screen"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnTime"
                app:layout_constraintStart_toEndOf="@id/btnGrid"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/btnTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:padding="@dimen/_10dp"
                android:src="@drawable/ic_time_camera_screen"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/btnSettings"
                app:layout_constraintStart_toEndOf="@id/btnFlash"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageButton
                android:id="@+id/btnSettings"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:padding="@dimen/_10dp"
                android:src="@drawable/ic_settings_camera_screen"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/btnTime"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Countdown Timer -->
        <TextView
            android:id="@+id/tvCountdownTimerPicture"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/font_400"
            android:textColor="@color/black"
            android:textSize="100sp"
            app:layout_constraintBottom_toBottomOf="@+id/frameLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/topIcons"
            tools:text="10"
            tools:textColor="@color/white" />

        <!-- Bottom panel -->
        <LinearLayout
            android:id="@+id/bottomPanel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@color/black"
            android:gravity="center"
            android:orientation="vertical"
            android:paddingVertical="@dimen/_10dp"
            app:layout_constraintBottom_toTopOf="@id/layoutAds"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1">

                    <ImageView
                        android:id="@+id/imgSavePhotoContent"
                        android:layout_width="@dimen/_48dp"
                        android:layout_height="@dimen/_48dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_gallery_camera_screen" />

                    <ImageView
                        android:id="@+id/btnPauseOrResumeVideoRecording"
                        android:layout_width="@dimen/_48dp"
                        android:layout_height="@dimen/_48dp"
                        android:layout_gravity="center"
                        android:src="@drawable/ic_resume_recording_camera_screen"
                        android:visibility="gone" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1">

                    <ImageButton
                        android:id="@+id/btnCapture"
                        android:layout_width="@dimen/_64dp"
                        android:layout_height="@dimen/_64dp"
                        android:layout_gravity="center"
                        android:background="@null"
                        android:src="@drawable/ic_capture_camera_screen" />
                </FrameLayout>

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_weight="1">

                    <ImageButton
                        android:id="@+id/btnSwitchCamera"
                        android:layout_width="@dimen/_48dp"
                        android:layout_height="@dimen/_48dp"
                        android:layout_gravity="center"
                        android:background="@null"
                        android:src="@drawable/ic_swap_facing_camera_screen" />
                </FrameLayout>

            </LinearLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/clToggleContainer"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/_48dp"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/_10dp"
                android:background="@drawable/bg_tab_unselected"
                android:clipToOutline="true"
                android:padding="@dimen/_4dp">

                <LinearLayout
                    android:id="@+id/tab_container"
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tab_video"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_marginEnd="4dp"
                        android:background="@drawable/tab_background_selector"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/_20dp"
                        android:text="@string/video"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_16sp" />

                    <TextView
                        android:id="@+id/tab_photo"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:background="@drawable/tab_background_selector"
                        android:gravity="center"
                        android:paddingHorizontal="@dimen/_20dp"
                        android:text="@string/photo"
                        android:textColor="@color/white"
                        android:textSize="@dimen/_16sp" />
                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <TextView
                android:id="@+id/tvRecordingTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_10dp"
                android:background="@drawable/bg_radius_20"
                android:backgroundTint="@color/black_121825"
                android:padding="@dimen/_10dp"
                android:textColor="@android:color/white"
                android:textSize="@dimen/_16sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="00:00:00" />
        </LinearLayout>

        <!-- Lớp phủ kết quả ảnh -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/previewOverlay"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/black"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/previewImage"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="fitCenter"
                app:layout_constraintBottom_toTopOf="@+id/previewButtonsPanel"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:id="@+id/previewButtonsPanel"
                android:layout_width="0dp"
                android:layout_height="@dimen/_80dp"
                android:background="@color/black"
                android:gravity="center"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <Button
                    android:id="@+id/btnCancel"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="32dp"
                    android:text="Cancel" />

                <Button
                    android:id="@+id/btnAccept"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="32dp"
                    android:text="Accept" />
            </LinearLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Thêm vào layout hiện tại -->

        <FrameLayout
            android:id="@+id/layoutAds"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="360:70">

            <FrameLayout
                android:id="@+id/adViewGroup"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_gravity="bottom"
                android:background="#D7D6D6">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fontFamily="@font/font_400"
                    android:gravity="center"
                    android:text="@string/loading_ads"
                    android:textColor="@color/black"
                    android:textSize="@dimen/_16sp" />

            </FrameLayout>
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>