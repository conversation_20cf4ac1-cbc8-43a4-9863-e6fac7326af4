package co.cameralocation.framework.presentation.model.livevideo

import android.os.Parcelable
import co.cameralocation.BuildConfig
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class Panorama(
    @SerializedName("id")
    val id: Int,
    @SerializedName("name")
    val name: String,
    @SerializedName("image")
    val image: String
) : Parcelable

fun Panorama.toLocalModel(): PanoramaLocal {
    return PanoramaLocal(
        id = this.id,
        name = this.name,
        imageUrl = BuildConfig.BASE_SATELLITE_URL + this.image,
    )
}
